{"name": "shona-dictionary-api", "version": "1.0.0", "description": "A comprehensive API for Shona language dictionary with words and proverbs, supporting both MongoDB and PostgreSQL", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest test/unit", "test:integration": "jest test/integration", "lint": "eslint src/ test/", "lint:fix": "eslint src/ test/ --fix", "format": "prettier --write \"src/**/*.js\" \"test/**/*.js\"", "import:mongo": "node scripts/import_mongo.js", "import:postgres": "node scripts/import_postgres.js", "import:mongo:clear": "node scripts/import_mongo.js --clear", "import:postgres:clear": "node scripts/import_postgres.js --clear --migrate", "seed:mongo": "npm run import:mongo:clear", "seed:postgres": "npm run import:postgres:clear", "migrate:postgres": "node scripts/import_postgres.js --migrate", "docs": "jsdoc -c jsdoc.conf.json", "validate": "npm run lint && npm run test", "prepare": "husky install"}, "keywords": ["shona", "dictionary", "api", "zimbabwe", "language", "translation", "proverbs", "mongodb", "postgresql", "nodejs", "express"], "author": {"name": "Your Name", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/shona-dictionary-api.git"}, "bugs": {"url": "https://github.com/yourusername/shona-dictionary-api/issues"}, "homepage": "https://github.com/yourusername/shona-dictionary-api#readme", "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "pg": "^8.11.3", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "express-rate-limit": "^6.10.0", "dotenv": "^16.3.1", "joi": "^17.9.2", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"jest": "^29.6.2", "supertest": "^6.3.3", "nodemon": "^3.0.1", "eslint": "^8.47.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest": "^27.2.3", "prettier": "^3.0.2", "husky": "^8.0.3", "lint-staged": "^13.2.3", "jsdoc": "^4.0.2"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/app.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "testMatch": ["**/test/**/*.test.js"], "setupFilesAfterEnv": ["<rootDir>/test/setup.js"]}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}}