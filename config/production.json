{"app": {"name": "Shona Dictionary API", "version": "1.0.0", "description": "A comprehensive API for Shona language dictionary with words and proverbs", "port": 3000}, "mongodb": {"uri": "mongodb://localhost:27017/shona_dictionary_prod", "options": {"useNewUrlParser": true, "useUnifiedTopology": true, "maxPoolSize": 20, "serverSelectionTimeoutMS": 5000, "socketTimeoutMS": 45000, "retryWrites": true, "w": "majority"}}, "postgres": {"host": "localhost", "port": 5432, "database": "shona_dictionary_prod", "user": "postgres", "password": "password", "ssl": true, "pool": {"min": 5, "max": 20, "idleTimeoutMillis": 30000, "connectionTimeoutMillis": 2000}}, "api": {"rateLimit": {"windowMs": 900000, "max": 1000}, "pagination": {"defaultLimit": 20, "maxLimit": 50}, "cors": {"origin": ["https://yourdomain.com", "https://api.yourdomain.com"], "methods": ["GET", "POST", "PUT", "DELETE"], "allowedHeaders": ["Content-Type", "Authorization"], "credentials": true}}, "logging": {"level": "warn", "format": "combined"}, "security": {"helmet": {"contentSecurityPolicy": {"directives": {"defaultSrc": ["'self'"], "styleSrc": ["'self'", "'unsafe-inline'"], "scriptSrc": ["'self'"], "imgSrc": ["'self'", "data:", "https:"]}}, "crossOriginEmbedderPolicy": true, "hsts": {"maxAge": 31536000, "includeSubDomains": true, "preload": true}}}}