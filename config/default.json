{"app": {"name": "Shona Dictionary API", "version": "1.0.0", "description": "A comprehensive API for Shona language dictionary with words and proverbs", "port": 3000}, "mongodb": {"uri": "mongodb://localhost:27017/shona_dictionary", "options": {"useNewUrlParser": true, "useUnifiedTopology": true, "maxPoolSize": 10, "serverSelectionTimeoutMS": 5000, "socketTimeoutMS": 45000}}, "postgres": {"host": "localhost", "port": 5432, "database": "shona_dictionary", "user": "postgres", "password": "password", "ssl": false, "pool": {"min": 2, "max": 10, "idleTimeoutMillis": 30000, "connectionTimeoutMillis": 2000}}, "api": {"rateLimit": {"windowMs": 900000, "max": 100}, "pagination": {"defaultLimit": 20, "maxLimit": 100}, "cors": {"origin": "*", "methods": ["GET", "POST", "PUT", "DELETE"], "allowedHeaders": ["Content-Type", "Authorization"]}}, "logging": {"level": "info", "format": "combined"}, "security": {"helmet": {"contentSecurityPolicy": false, "crossOriginEmbedderPolicy": false}}}