# Shona Dictionary API

A comprehensive RESTful API for the Shona language dictionary, featuring words, definitions, examples, and traditional proverbs. The API supports both MongoDB and PostgreSQL databases and provides powerful search capabilities.

## Features

- 🔍 **Advanced Search**: Full-text search across Shona and English words
- 📚 **Comprehensive Dictionary**: Words with definitions, examples, and pronunciation guides
- 🎭 **Proverbs Collection**: Traditional Shona proverbs with meanings and context
- 🗄️ **Dual Database Support**: Choose between MongoDB or PostgreSQL
- 🚀 **High Performance**: Optimized queries with proper indexing
- 📖 **RESTful API**: Clean, intuitive API design
- 🔒 **Security**: Rate limiting, CORS, and security headers
- ✅ **Well Tested**: Comprehensive unit and integration tests
- 📊 **Pagination**: Efficient pagination for large datasets

## Quick Start

### Prerequisites

- Node.js (v14 or higher)
- MongoDB OR PostgreSQL
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/shona-dictionary-api.git
cd shona-dictionary-api
```

2. Install dependencies:
```bash
npm install
```

3. Configure environment variables:
```bash
cp .env.example .env
# Edit .env with your database configuration
```

4. Choose your database and set up:

**For MongoDB:**
```bash
# Set DB_TYPE=mongodb in .env
npm run seed:mongo
```

**For PostgreSQL:**
```bash
# Set DB_TYPE=postgres in .env
npm run seed:postgres
```

5. Start the server:
```bash
npm run dev
```

The API will be available at `http://localhost:3000`

## API Endpoints

### Words

- `GET /api/words` - Get all words (with pagination and filtering)
- `GET /api/words/search?q=term` - Search words
- `GET /api/words/:id` - Get word by ID
- `POST /api/words` - Create new word
- `PUT /api/words/:id` - Update word
- `DELETE /api/words/:id` - Delete word

### Proverbs

- `GET /api/proverbs` - Get all proverbs
- `GET /api/proverbs/search?q=term` - Search proverbs
- `GET /api/proverbs/:id` - Get proverb by ID
- `GET /api/proverbs/random` - Get random proverb
- `POST /api/proverbs` - Create new proverb
- `PUT /api/proverbs/:id` - Update proverb
- `DELETE /api/proverbs/:id` - Delete proverb

### System

- `GET /api/health` - Health check
- `GET /api` - API documentation

## Usage Examples

### Search for words
```bash
curl "http://localhost:3000/api/words/search?q=mhuri"
```

### Get words with filtering
```bash
curl "http://localhost:3000/api/words?difficulty=beginner&partOfSpeech=noun&page=1&limit=10"
```

### Create a new word
```bash
curl -X POST "http://localhost:3000/api/words" \
  -H "Content-Type: application/json" \
  -d '{
    "shona": "musha",
    "english": "home",
    "partOfSpeech": "noun",
    "definition": "A place where one lives",
    "difficulty": "beginner"
  }'
```

## Database Configuration

### MongoDB Setup

1. Install MongoDB locally or use MongoDB Atlas
2. Set environment variables:
```env
DB_TYPE=mongodb
MONGODB_URI=mongodb://localhost:27017/shona_dictionary
```

### PostgreSQL Setup

1. Install PostgreSQL locally or use a cloud service
2. Set environment variables:
```env
DB_TYPE=postgres
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=shona_dictionary
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password
```

## Development

### Running Tests
```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test suites
npm run test:unit
npm run test:integration

# Watch mode
npm run test:watch
```

### Code Quality
```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

### Data Import
```bash
# Import sample data to MongoDB
npm run import:mongo

# Import sample data to PostgreSQL
npm run import:postgres

# Clear and reimport data
npm run seed:mongo
npm run seed:postgres
```

## Project Structure

```
shona-dictionary-api/
├── .vscode/                 # VS Code configurations
├── src/
│   ├── db/
│   │   ├── mongo/          # MongoDB-specific files
│   │   │   ├── models/     # Mongoose models
│   │   │   └── seed/       # Sample data
│   │   └── postgres/       # PostgreSQL-specific files
│   │       ├── migrations/ # Database migrations
│   │       └── seed.sql    # Sample data
│   ├── api/
│   │   ├── controllers/    # Route controllers
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   └── app.js              # Main application entry
├── test/
│   ├── unit/               # Unit tests
│   └── integration/        # Integration tests
├── scripts/                # Utility scripts
├── config/                 # Configuration files
└── README.md
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Shona language community
- Contributors and maintainers
- Open source libraries used in this project
