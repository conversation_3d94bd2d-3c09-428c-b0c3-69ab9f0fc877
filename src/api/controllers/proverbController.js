const proverbService = require('../services/searchService');
const { validateProverb } = require('../utils/validation');
const { successResponse, errorResponse } = require('../utils/response');

class ProverbController {
  // Get all proverbs with pagination and filtering
  async getAllProverbs(req, res) {
    try {
      const { page = 1, limit = 20, difficulty, category, region } = req.query;
      
      const filters = {};
      if (difficulty) filters.difficulty = difficulty;
      if (category) filters.category = category;
      if (region) filters.region = region;

      const result = await proverbService.getAllProverbs({
        page: parseInt(page),
        limit: parseInt(limit),
        filters
      });

      return successResponse(res, result, 'Proverbs retrieved successfully');
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }

  // Search proverbs
  async searchProverbs(req, res) {
    try {
      const { q, lang = 'both', page = 1, limit = 20 } = req.query;
      
      if (!q) {
        return errorResponse(res, 'Search query is required', 400);
      }

      const result = await proverbService.searchProverbs({
        query: q,
        language: lang,
        page: parseInt(page),
        limit: parseInt(limit)
      });

      return successResponse(res, result, 'Search completed successfully');
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }

  // Get proverb by ID
  async getProverbById(req, res) {
    try {
      const { id } = req.params;
      const proverb = await proverbService.getProverbById(id);
      
      if (!proverb) {
        return errorResponse(res, 'Proverb not found', 404);
      }

      return successResponse(res, proverb, 'Proverb retrieved successfully');
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }

  // Create new proverb
  async createProverb(req, res) {
    try {
      const validation = validateProverb(req.body);
      if (!validation.isValid) {
        return errorResponse(res, validation.errors, 400);
      }

      const proverb = await proverbService.createProverb(req.body);
      return successResponse(res, proverb, 'Proverb created successfully', 201);
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }

  // Update proverb
  async updateProverb(req, res) {
    try {
      const { id } = req.params;
      const validation = validateProverb(req.body, false); // partial validation for updates
      
      if (!validation.isValid) {
        return errorResponse(res, validation.errors, 400);
      }

      const proverb = await proverbService.updateProverb(id, req.body);
      
      if (!proverb) {
        return errorResponse(res, 'Proverb not found', 404);
      }

      return successResponse(res, proverb, 'Proverb updated successfully');
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }

  // Delete proverb
  async deleteProverb(req, res) {
    try {
      const { id } = req.params;
      const deleted = await proverbService.deleteProverb(id);
      
      if (!deleted) {
        return errorResponse(res, 'Proverb not found', 404);
      }

      return successResponse(res, null, 'Proverb deleted successfully');
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }

  // Get random proverb
  async getRandomProverb(req, res) {
    try {
      const { difficulty, category } = req.query;
      const filters = {};
      if (difficulty) filters.difficulty = difficulty;
      if (category) filters.category = category;

      const proverb = await proverbService.getRandomProverb(filters);
      
      if (!proverb) {
        return errorResponse(res, 'No proverbs found', 404);
      }

      return successResponse(res, proverb, 'Random proverb retrieved successfully');
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }
}

module.exports = new ProverbController();
