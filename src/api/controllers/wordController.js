const wordService = require('../services/searchService');
const { validateWord } = require('../utils/validation');
const { successResponse, errorResponse } = require('../utils/response');

class WordController {
  // Get all words with pagination and filtering
  async getAllWords(req, res) {
    try {
      const { page = 1, limit = 20, difficulty, partOfSpeech, tags } = req.query;
      
      const filters = {};
      if (difficulty) filters.difficulty = difficulty;
      if (partOfSpeech) filters.partOfSpeech = partOfSpeech;
      if (tags) filters.tags = tags.split(',');

      const result = await wordService.getAllWords({
        page: parseInt(page),
        limit: parseInt(limit),
        filters
      });

      return successResponse(res, result, 'Words retrieved successfully');
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }

  // Search words
  async searchWords(req, res) {
    try {
      const { q, lang = 'both', page = 1, limit = 20 } = req.query;
      
      if (!q) {
        return errorResponse(res, 'Search query is required', 400);
      }

      const result = await wordService.searchWords({
        query: q,
        language: lang,
        page: parseInt(page),
        limit: parseInt(limit)
      });

      return successResponse(res, result, 'Search completed successfully');
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }

  // Get word by ID
  async getWordById(req, res) {
    try {
      const { id } = req.params;
      const word = await wordService.getWordById(id);
      
      if (!word) {
        return errorResponse(res, 'Word not found', 404);
      }

      return successResponse(res, word, 'Word retrieved successfully');
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }

  // Create new word
  async createWord(req, res) {
    try {
      const validation = validateWord(req.body);
      if (!validation.isValid) {
        return errorResponse(res, validation.errors, 400);
      }

      const word = await wordService.createWord(req.body);
      return successResponse(res, word, 'Word created successfully', 201);
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }

  // Update word
  async updateWord(req, res) {
    try {
      const { id } = req.params;
      const validation = validateWord(req.body, false); // partial validation for updates
      
      if (!validation.isValid) {
        return errorResponse(res, validation.errors, 400);
      }

      const word = await wordService.updateWord(id, req.body);
      
      if (!word) {
        return errorResponse(res, 'Word not found', 404);
      }

      return successResponse(res, word, 'Word updated successfully');
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }

  // Delete word
  async deleteWord(req, res) {
    try {
      const { id } = req.params;
      const deleted = await wordService.deleteWord(id);
      
      if (!deleted) {
        return errorResponse(res, 'Word not found', 404);
      }

      return successResponse(res, null, 'Word deleted successfully');
    } catch (error) {
      return errorResponse(res, error.message, 500);
    }
  }
}

module.exports = new WordController();
