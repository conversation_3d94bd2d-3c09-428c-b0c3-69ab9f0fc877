// Validation utilities for API requests

const validateWord = (wordData, isRequired = true) => {
  const errors = [];
  
  // Required fields validation
  if (isRequired) {
    if (!wordData.shona || typeof wordData.shona !== 'string' || wordData.shona.trim().length === 0) {
      errors.push('Shona word is required and must be a non-empty string');
    }
    
    if (!wordData.english || typeof wordData.english !== 'string' || wordData.english.trim().length === 0) {
      errors.push('English translation is required and must be a non-empty string');
    }
    
    if (!wordData.partOfSpeech) {
      errors.push('Part of speech is required');
    }
    
    if (!wordData.definition || typeof wordData.definition !== 'string' || wordData.definition.trim().length === 0) {
      errors.push('Definition is required and must be a non-empty string');
    }
  }
  
  // Part of speech validation
  const validPartsOfSpeech = ['noun', 'verb', 'adjective', 'adverb', 'pronoun', 'preposition', 'conjunction', 'interjection'];
  if (wordData.partOfSpeech && !validPartsOfSpeech.includes(wordData.partOfSpeech)) {
    errors.push(`Part of speech must be one of: ${validPartsOfSpeech.join(', ')}`);
  }
  
  // Difficulty validation
  const validDifficulties = ['beginner', 'intermediate', 'advanced'];
  if (wordData.difficulty && !validDifficulties.includes(wordData.difficulty)) {
    errors.push(`Difficulty must be one of: ${validDifficulties.join(', ')}`);
  }
  
  // Examples validation
  if (wordData.examples) {
    if (!Array.isArray(wordData.examples)) {
      errors.push('Examples must be an array');
    } else {
      wordData.examples.forEach((example, index) => {
        if (!example.shona || !example.english) {
          errors.push(`Example ${index + 1} must have both shona and english properties`);
        }
      });
    }
  }
  
  // Tags validation
  if (wordData.tags && !Array.isArray(wordData.tags)) {
    errors.push('Tags must be an array');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

const validateProverb = (proverbData, isRequired = true) => {
  const errors = [];
  
  // Required fields validation
  if (isRequired) {
    if (!proverbData.shona || typeof proverbData.shona !== 'string' || proverbData.shona.trim().length === 0) {
      errors.push('Shona proverb is required and must be a non-empty string');
    }
    
    if (!proverbData.english || typeof proverbData.english !== 'string' || proverbData.english.trim().length === 0) {
      errors.push('English translation is required and must be a non-empty string');
    }
    
    if (!proverbData.meaning || typeof proverbData.meaning !== 'string' || proverbData.meaning.trim().length === 0) {
      errors.push('Meaning is required and must be a non-empty string');
    }
  }
  
  // Category validation
  const validCategories = ['wisdom', 'warning', 'advice', 'observation', 'humor'];
  if (proverbData.category && !validCategories.includes(proverbData.category)) {
    errors.push(`Category must be one of: ${validCategories.join(', ')}`);
  }
  
  // Difficulty validation
  const validDifficulties = ['beginner', 'intermediate', 'advanced'];
  if (proverbData.difficulty && !validDifficulties.includes(proverbData.difficulty)) {
    errors.push(`Difficulty must be one of: ${validDifficulties.join(', ')}`);
  }
  
  // Tags validation
  if (proverbData.tags && !Array.isArray(proverbData.tags)) {
    errors.push('Tags must be an array');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

const validateSearchQuery = (query) => {
  const errors = [];
  
  if (!query || typeof query !== 'string' || query.trim().length === 0) {
    errors.push('Search query is required and must be a non-empty string');
  }
  
  if (query && query.length > 100) {
    errors.push('Search query must be less than 100 characters');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

const validatePagination = (page, limit) => {
  const errors = [];
  
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  
  if (isNaN(pageNum) || pageNum < 1) {
    errors.push('Page must be a positive integer');
  }
  
  if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
    errors.push('Limit must be a positive integer between 1 and 100');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    page: pageNum,
    limit: limitNum
  };
};

module.exports = {
  validateWord,
  validateProverb,
  validateSearchQuery,
  validatePagination
};
