// Database abstraction layer for search operations
const dbType = process.env.DB_TYPE || 'mongodb'; // 'mongodb' or 'postgres'

let wordModel, proverbModel, dbConnection;

if (dbType === 'mongodb') {
  wordModel = require('../../db/mongo/models/Word');
  proverbModel = require('../../db/mongo/models/Proverb');
  dbConnection = require('../../db/mongo/connection');
} else {
  dbConnection = require('../../db/postgres/connection');
}

class SearchService {
  // Word operations
  async getAllWords({ page = 1, limit = 20, filters = {} }) {
    if (dbType === 'mongodb') {
      return this.getWordsFromMongo({ page, limit, filters });
    } else {
      return this.getWordsFromPostgres({ page, limit, filters });
    }
  }

  async searchWords({ query, language = 'both', page = 1, limit = 20 }) {
    if (dbType === 'mongodb') {
      return this.searchWordsInMongo({ query, language, page, limit });
    } else {
      return this.searchWordsInPostgres({ query, language, page, limit });
    }
  }

  async getWordById(id) {
    if (dbType === 'mongodb') {
      return await wordModel.findById(id);
    } else {
      const result = await dbConnection.query('SELECT * FROM words WHERE id = $1', [id]);
      return result.rows[0] || null;
    }
  }

  async createWord(wordData) {
    if (dbType === 'mongodb') {
      const word = new wordModel(wordData);
      return await word.save();
    } else {
      const { shona, english, partOfSpeech, definition, examples, pronunciation, difficulty, tags } = wordData;
      const result = await dbConnection.query(
        `INSERT INTO words (shona, english, part_of_speech, definition, examples, pronunciation, difficulty, tags) 
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
        [shona, english, partOfSpeech, definition, JSON.stringify(examples || []), pronunciation, difficulty, tags || []]
      );
      return result.rows[0];
    }
  }

  async updateWord(id, updateData) {
    if (dbType === 'mongodb') {
      return await wordModel.findByIdAndUpdate(id, updateData, { new: true });
    } else {
      const fields = [];
      const values = [];
      let paramCount = 1;

      Object.keys(updateData).forEach(key => {
        if (key === 'partOfSpeech') {
          fields.push(`part_of_speech = $${paramCount}`);
        } else if (key === 'examples') {
          fields.push(`examples = $${paramCount}`);
          values.push(JSON.stringify(updateData[key]));
          paramCount++;
          return;
        } else {
          fields.push(`${key} = $${paramCount}`);
        }
        values.push(updateData[key]);
        paramCount++;
      });

      values.push(id);
      const result = await dbConnection.query(
        `UPDATE words SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = $${paramCount} RETURNING *`,
        values
      );
      return result.rows[0] || null;
    }
  }

  async deleteWord(id) {
    if (dbType === 'mongodb') {
      const result = await wordModel.findByIdAndDelete(id);
      return !!result;
    } else {
      const result = await dbConnection.query('DELETE FROM words WHERE id = $1', [id]);
      return result.rowCount > 0;
    }
  }

  // MongoDB specific methods
  async getWordsFromMongo({ page, limit, filters }) {
    const skip = (page - 1) * limit;
    const query = {};
    
    if (filters.difficulty) query.difficulty = filters.difficulty;
    if (filters.partOfSpeech) query.partOfSpeech = filters.partOfSpeech;
    if (filters.tags) query.tags = { $in: filters.tags };

    const [words, total] = await Promise.all([
      wordModel.find(query).skip(skip).limit(limit).sort({ createdAt: -1 }),
      wordModel.countDocuments(query)
    ]);

    return {
      words,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  async searchWordsInMongo({ query, language, page, limit }) {
    const skip = (page - 1) * limit;
    let searchQuery = {};

    if (language === 'shona') {
      searchQuery = { $text: { $search: query } };
    } else if (language === 'english') {
      searchQuery = { $text: { $search: query } };
    } else {
      searchQuery = { $text: { $search: query } };
    }

    const [words, total] = await Promise.all([
      wordModel.find(searchQuery, { score: { $meta: 'textScore' } })
        .sort({ score: { $meta: 'textScore' } })
        .skip(skip)
        .limit(limit),
      wordModel.countDocuments(searchQuery)
    ]);

    return {
      words,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  // PostgreSQL specific methods
  async getWordsFromPostgres({ page, limit, filters }) {
    const offset = (page - 1) * limit;
    let whereClause = '';
    const values = [];
    let paramCount = 1;

    const conditions = [];
    if (filters.difficulty) {
      conditions.push(`difficulty = $${paramCount}`);
      values.push(filters.difficulty);
      paramCount++;
    }
    if (filters.partOfSpeech) {
      conditions.push(`part_of_speech = $${paramCount}`);
      values.push(filters.partOfSpeech);
      paramCount++;
    }
    if (filters.tags && filters.tags.length > 0) {
      conditions.push(`tags && $${paramCount}`);
      values.push(filters.tags);
      paramCount++;
    }

    if (conditions.length > 0) {
      whereClause = 'WHERE ' + conditions.join(' AND ');
    }

    values.push(limit, offset);
    const [wordsResult, countResult] = await Promise.all([
      dbConnection.query(
        `SELECT * FROM words ${whereClause} ORDER BY created_at DESC LIMIT $${paramCount} OFFSET $${paramCount + 1}`,
        values
      ),
      dbConnection.query(`SELECT COUNT(*) FROM words ${whereClause}`, values.slice(0, -2))
    ]);

    return {
      words: wordsResult.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].count),
        pages: Math.ceil(parseInt(countResult.rows[0].count) / limit)
      }
    };
  }

  async searchWordsInPostgres({ query, language, page, limit }) {
    const offset = (page - 1) * limit;
    let searchCondition = '';
    
    if (language === 'shona') {
      searchCondition = "to_tsvector('english', shona) @@ plainto_tsquery('english', $1)";
    } else if (language === 'english') {
      searchCondition = "to_tsvector('english', english || ' ' || definition) @@ plainto_tsquery('english', $1)";
    } else {
      searchCondition = "to_tsvector('english', shona || ' ' || english || ' ' || definition) @@ plainto_tsquery('english', $1)";
    }

    const [wordsResult, countResult] = await Promise.all([
      dbConnection.query(
        `SELECT *, ts_rank(to_tsvector('english', shona || ' ' || english || ' ' || definition), plainto_tsquery('english', $1)) as rank
         FROM words 
         WHERE ${searchCondition}
         ORDER BY rank DESC 
         LIMIT $2 OFFSET $3`,
        [query, limit, offset]
      ),
      dbConnection.query(
        `SELECT COUNT(*) FROM words WHERE ${searchCondition}`,
        [query]
      )
    ]);

    return {
      words: wordsResult.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].count),
        pages: Math.ceil(parseInt(countResult.rows[0].count) / limit)
      }
    };
  }
}

module.exports = new SearchService();
