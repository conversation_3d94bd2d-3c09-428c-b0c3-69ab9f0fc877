const express = require('express');
const wordController = require('../controllers/wordController');

const router = express.Router();

/**
 * @route   GET /api/words
 * @desc    Get all words with pagination and filtering
 * @access  Public
 * @params  page, limit, difficulty, partOfSpeech, tags
 */
router.get('/', wordController.getAllWords);

/**
 * @route   GET /api/words/search
 * @desc    Search words
 * @access  Public
 * @params  q (query), lang (language), page, limit
 */
router.get('/search', wordController.searchWords);

/**
 * @route   GET /api/words/:id
 * @desc    Get word by ID
 * @access  Public
 */
router.get('/:id', wordController.getWordById);

/**
 * @route   POST /api/words
 * @desc    Create new word
 * @access  Private (would require authentication in production)
 */
router.post('/', wordController.createWord);

/**
 * @route   PUT /api/words/:id
 * @desc    Update word
 * @access  Private (would require authentication in production)
 */
router.put('/:id', wordController.updateWord);

/**
 * @route   DELETE /api/words/:id
 * @desc    Delete word
 * @access  Private (would require authentication in production)
 */
router.delete('/:id', wordController.deleteWord);

module.exports = router;
