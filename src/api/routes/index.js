const express = require('express');
const wordRoutes = require('./wordRoutes');

const router = express.Router();

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Shona Dictionary API is running',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API routes
router.use('/words', wordRoutes);

// API documentation endpoint
router.get('/', (req, res) => {
  res.json({
    message: 'Welcome to Shona Dictionary API',
    version: '1.0.0',
    endpoints: {
      health: 'GET /api/health',
      words: {
        getAll: 'GET /api/words',
        search: 'GET /api/words/search?q=term',
        getById: 'GET /api/words/:id',
        create: 'POST /api/words',
        update: 'PUT /api/words/:id',
        delete: 'DELETE /api/words/:id'
      },
      proverbs: {
        getAll: 'GET /api/proverbs',
        search: 'GET /api/proverbs/search?q=term',
        getById: 'GET /api/proverbs/:id',
        getRandom: 'GET /api/proverbs/random',
        create: 'POST /api/proverbs',
        update: 'PUT /api/proverbs/:id',
        delete: 'DELETE /api/proverbs/:id'
      }
    },
    documentation: 'Visit /api/docs for detailed API documentation'
  });
});

module.exports = router;
