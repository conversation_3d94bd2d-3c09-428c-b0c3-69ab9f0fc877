-- Create words table
CREATE TABLE IF NOT EXISTS words (
    id SERIAL PRIMARY KEY,
    shona VARCHAR(255) NOT NULL,
    english VARCHAR(255) NOT NULL,
    part_of_speech VARCHAR(50) NOT NULL CHECK (part_of_speech IN ('noun', 'verb', 'adjective', 'adverb', 'pronoun', 'preposition', 'conjunction', 'interjection')),
    definition TEXT NOT NULL,
    examples JSONB DEFAULT '[]',
    pronunciation VARCHAR(255),
    difficulty VARCHAR(20) DEFAULT 'beginner' CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create proverbs table
CREATE TABLE IF NOT EXISTS proverbs (
    id SERIAL PRIMARY KEY,
    shona TEXT NOT NULL UNIQUE,
    english TEXT NOT NULL,
    meaning TEXT NOT NULL,
    context TEXT,
    category VARCHAR(50) DEFAULT 'wisdom' CHECK (category IN ('wisdom', 'warning', 'advice', 'observation', 'humor')),
    region VARCHAR(100),
    difficulty VARCHAR(20) DEFAULT 'intermediate' CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better search performance
CREATE INDEX IF NOT EXISTS idx_words_shona ON words USING gin(to_tsvector('english', shona));
CREATE INDEX IF NOT EXISTS idx_words_english ON words USING gin(to_tsvector('english', english));
CREATE INDEX IF NOT EXISTS idx_words_definition ON words USING gin(to_tsvector('english', definition));
CREATE INDEX IF NOT EXISTS idx_words_part_of_speech ON words(part_of_speech);
CREATE INDEX IF NOT EXISTS idx_words_difficulty ON words(difficulty);

CREATE INDEX IF NOT EXISTS idx_proverbs_shona ON proverbs USING gin(to_tsvector('english', shona));
CREATE INDEX IF NOT EXISTS idx_proverbs_english ON proverbs USING gin(to_tsvector('english', english));
CREATE INDEX IF NOT EXISTS idx_proverbs_meaning ON proverbs USING gin(to_tsvector('english', meaning));
CREATE INDEX IF NOT EXISTS idx_proverbs_category ON proverbs(category);
CREATE INDEX IF NOT EXISTS idx_proverbs_difficulty ON proverbs(difficulty);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_words_updated_at BEFORE UPDATE ON words
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_proverbs_updated_at BEFORE UPDATE ON proverbs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
