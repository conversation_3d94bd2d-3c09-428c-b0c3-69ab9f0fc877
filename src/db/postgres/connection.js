const { Pool } = require('pg');
const config = require('../../../config/default.json');

class PostgresConnection {
  constructor() {
    this.pool = null;
  }

  async connect() {
    try {
      const dbConfig = {
        user: process.env.POSTGRES_USER || config.postgres.user,
        host: process.env.POSTGRES_HOST || config.postgres.host,
        database: process.env.POSTGRES_DB || config.postgres.database,
        password: process.env.POSTGRES_PASSWORD || config.postgres.password,
        port: process.env.POSTGRES_PORT || config.postgres.port,
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
      };

      this.pool = new Pool(dbConfig);

      // Test the connection
      const client = await this.pool.connect();
      console.log('PostgreSQL connected successfully');
      client.release();

      return this.pool;
    } catch (error) {
      console.error('PostgreSQL connection error:', error);
      throw error;
    }
  }

  async disconnect() {
    if (this.pool) {
      await this.pool.end();
      console.log('PostgreSQL disconnected');
    }
  }

  getPool() {
    return this.pool;
  }

  async query(text, params) {
    const client = await this.pool.connect();
    try {
      const result = await client.query(text, params);
      return result;
    } finally {
      client.release();
    }
  }
}

module.exports = new PostgresConnection();
