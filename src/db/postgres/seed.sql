-- Insert sample words
INSERT INTO words (shona, english, part_of_speech, definition, examples, pronunciation, difficulty, tags) VALUES
('mhuri', 'family', 'noun', 'A group of people related by blood or marriage', 
 '[{"shona": "Mhuri yangu inogara muHarare", "english": "My family lives in Harare"}]', 
 'mhu-ri', 'beginner', '{"family", "relationships"}'),

('kudya', 'to eat', 'verb', 'To consume food', 
 '[{"shona": "Ndinoda kudya sadza", "english": "I want to eat sadza"}]', 
 'ku-dya', 'beginner', '{"food", "actions"}'),

('rudo', 'love', 'noun', 'A strong feeling of affection', 
 '[{"shona": "Rudo rwangu rwakakura", "english": "My love is great"}]', 
 'ru-do', 'intermediate', '{"emotions", "relationships"}'),

('musha', 'home', 'noun', 'A place where one lives', 
 '[{"shona": "Ndinoda kuenda kumusha", "english": "I want to go home"}]', 
 'mu-sha', 'beginner', '{"home", "places"}'),

('kufamba', 'to walk', 'verb', 'To move on foot', 
 '[{"shona": "Ndiri kufamba kuenda kuchikoro", "english": "I am walking to school"}]', 
 'ku-fam-ba', 'beginner', '{"movement", "actions"}');

-- Insert sample proverbs
INSERT INTO proverbs (shona, english, meaning, context, category, difficulty, tags) VALUES
('Chakafukidza dzimba matenga', 'What covers houses is the roof', 
 'Every problem has a solution, just as every house needs a roof for protection', 
 'Used when encouraging someone facing difficulties', 'wisdom', 'intermediate', 
 '{"wisdom", "solutions", "problems"}'),

('Chara chimwe hachitswanyi inda', 'One finger cannot crush a louse', 
 'Unity is strength; working together achieves more than working alone', 
 'Used to emphasize the importance of cooperation', 'wisdom', 'intermediate', 
 '{"unity", "cooperation", "teamwork"}'),

('Kukura kwemwana mukamwa', 'A child grows in the mouth', 
 'Children learn through listening and speaking; education comes through communication', 
 'Used to emphasize the importance of teaching children through conversation', 'advice', 'advanced', 
 '{"education", "children", "communication"}');
