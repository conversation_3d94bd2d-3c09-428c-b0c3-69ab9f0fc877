const mongoose = require('mongoose');

const wordSchema = new mongoose.Schema({
  shona: {
    type: String,
    required: true,
    index: true
  },
  english: {
    type: String,
    required: true,
    index: true
  },
  partOfSpeech: {
    type: String,
    enum: ['noun', 'verb', 'adjective', 'adverb', 'pronoun', 'preposition', 'conjunction', 'interjection'],
    required: true
  },
  definition: {
    type: String,
    required: true
  },
  examples: [{
    shona: String,
    english: String
  }],
  pronunciation: {
    type: String
  },
  difficulty: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    default: 'beginner'
  },
  tags: [String],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create text index for search functionality
wordSchema.index({
  shona: 'text',
  english: 'text',
  definition: 'text'
});

// Update the updatedAt field before saving
wordSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Word', wordSchema);
