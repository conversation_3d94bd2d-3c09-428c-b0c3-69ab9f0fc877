const mongoose = require('mongoose');

const proverbSchema = new mongoose.Schema({
  shona: {
    type: String,
    required: true,
    unique: true
  },
  english: {
    type: String,
    required: true
  },
  meaning: {
    type: String,
    required: true
  },
  context: {
    type: String
  },
  category: {
    type: String,
    enum: ['wisdom', 'warning', 'advice', 'observation', 'humor'],
    default: 'wisdom'
  },
  region: {
    type: String
  },
  difficulty: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    default: 'intermediate'
  },
  tags: [String],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create text index for search functionality
proverbSchema.index({
  shona: 'text',
  english: 'text',
  meaning: 'text'
});

// Update the updatedAt field before saving
proverbSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Proverb', proverbSchema);
