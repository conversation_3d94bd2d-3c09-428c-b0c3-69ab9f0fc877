#!/usr/bin/env node

/**
 * PostgreSQL Data Import Script
 * 
 * This script imports word and proverb data into PostgreSQL
 * Usage: node scripts/import_postgres.js [--words] [--proverbs] [--clear] [--migrate]
 */

const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

// Import database connection
const postgresConnection = require('../src/db/postgres/connection');

// Command line arguments
const args = process.argv.slice(2);
const shouldImportWords = args.includes('--words') || args.length === 0;
const shouldImportProverbs = args.includes('--proverbs') || args.length === 0;
const shouldClear = args.includes('--clear');
const shouldMigrate = args.includes('--migrate');

async function connectToDatabase() {
  try {
    await postgresConnection.connect();
    console.log('✅ Connected to PostgreSQL');
  } catch (error) {
    console.error('❌ PostgreSQL connection error:', error);
    process.exit(1);
  }
}

async function runMigrations() {
  if (!shouldMigrate) return;
  
  try {
    console.log('🔄 Running database migrations...');
    
    const migrationPath = path.join(__dirname, '../src/db/postgres/migrations/001_init_tables.sql');
    const migrationSQL = await fs.readFile(migrationPath, 'utf8');
    
    await postgresConnection.query(migrationSQL);
    console.log('✅ Database migrations completed');
    
  } catch (error) {
    console.error('❌ Error running migrations:', error);
    throw error;
  }
}

async function clearTables() {
  if (!shouldClear) return;
  
  try {
    if (shouldImportWords) {
      await postgresConnection.query('DELETE FROM words');
      console.log('🗑️  Cleared words table');
    }
    
    if (shouldImportProverbs) {
      await postgresConnection.query('DELETE FROM proverbs');
      console.log('🗑️  Cleared proverbs table');
    }
  } catch (error) {
    console.error('❌ Error clearing tables:', error);
    throw error;
  }
}

async function importWords() {
  if (!shouldImportWords) return;
  
  try {
    const wordsPath = path.join(__dirname, '../src/db/mongo/seed/words.json');
    const wordsData = await fs.readFile(wordsPath, 'utf8');
    const words = JSON.parse(wordsData);
    
    console.log(`📚 Importing ${words.length} words...`);
    
    for (const wordData of words) {
      try {
        const { shona, english, partOfSpeech, definition, examples, pronunciation, difficulty, tags } = wordData;
        
        await postgresConnection.query(
          `INSERT INTO words (shona, english, part_of_speech, definition, examples, pronunciation, difficulty, tags) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
          [
            shona,
            english,
            partOfSpeech,
            definition,
            JSON.stringify(examples || []),
            pronunciation,
            difficulty || 'beginner',
            tags || []
          ]
        );
        
        console.log(`  ✅ Imported: ${shona} -> ${english}`);
      } catch (error) {
        console.error(`  ❌ Failed to import word: ${wordData.shona}`, error.message);
      }
    }
    
    const result = await postgresConnection.query('SELECT COUNT(*) FROM words');
    const totalWords = parseInt(result.rows[0].count);
    console.log(`📊 Total words in database: ${totalWords}`);
    
  } catch (error) {
    console.error('❌ Error importing words:', error);
    throw error;
  }
}

async function importProverbs() {
  if (!shouldImportProverbs) return;
  
  try {
    // Sample proverbs data
    const proverbs = [
      {
        shona: "Chakafukidza dzimba matenga",
        english: "What covers houses is the roof",
        meaning: "Every problem has a solution, just as every house needs a roof for protection",
        context: "Used when encouraging someone facing difficulties",
        category: "wisdom",
        difficulty: "intermediate",
        tags: ["wisdom", "solutions", "problems"]
      },
      {
        shona: "Chara chimwe hachitswanyi inda",
        english: "One finger cannot crush a louse",
        meaning: "Unity is strength; working together achieves more than working alone",
        context: "Used to emphasize the importance of cooperation",
        category: "wisdom",
        difficulty: "intermediate",
        tags: ["unity", "cooperation", "teamwork"]
      },
      {
        shona: "Kukura kwemwana mukamwa",
        english: "A child grows in the mouth",
        meaning: "Children learn through listening and speaking; education comes through communication",
        context: "Used to emphasize the importance of teaching children through conversation",
        category: "advice",
        difficulty: "advanced",
        tags: ["education", "children", "communication"]
      }
    ];
    
    console.log(`🎭 Importing ${proverbs.length} proverbs...`);
    
    for (const proverbData of proverbs) {
      try {
        const { shona, english, meaning, context, category, region, difficulty, tags } = proverbData;
        
        await postgresConnection.query(
          `INSERT INTO proverbs (shona, english, meaning, context, category, region, difficulty, tags) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
          [
            shona,
            english,
            meaning,
            context,
            category || 'wisdom',
            region,
            difficulty || 'intermediate',
            tags || []
          ]
        );
        
        console.log(`  ✅ Imported: ${shona}`);
      } catch (error) {
        console.error(`  ❌ Failed to import proverb: ${proverbData.shona}`, error.message);
      }
    }
    
    const result = await postgresConnection.query('SELECT COUNT(*) FROM proverbs');
    const totalProverbs = parseInt(result.rows[0].count);
    console.log(`📊 Total proverbs in database: ${totalProverbs}`);
    
  } catch (error) {
    console.error('❌ Error importing proverbs:', error);
    throw error;
  }
}

async function runSeedSQL() {
  try {
    console.log('🌱 Running seed SQL...');
    
    const seedPath = path.join(__dirname, '../src/db/postgres/seed.sql');
    const seedSQL = await fs.readFile(seedPath, 'utf8');
    
    await postgresConnection.query(seedSQL);
    console.log('✅ Seed SQL completed');
    
  } catch (error) {
    console.error('❌ Error running seed SQL:', error);
    // Don't throw error as this is optional
  }
}

async function analyzeDatabase() {
  try {
    console.log('📊 Analyzing database...');
    
    if (shouldImportWords) {
      await postgresConnection.query('ANALYZE words');
      console.log('  ✅ Words table analyzed');
    }
    
    if (shouldImportProverbs) {
      await postgresConnection.query('ANALYZE proverbs');
      console.log('  ✅ Proverbs table analyzed');
    }
    
  } catch (error) {
    console.error('❌ Error analyzing database:', error);
    // Don't throw error as this is optional
  }
}

async function main() {
  try {
    console.log('🚀 Starting PostgreSQL data import...');
    console.log(`📋 Import options: words=${shouldImportWords}, proverbs=${shouldImportProverbs}, clear=${shouldClear}, migrate=${shouldMigrate}`);
    
    await connectToDatabase();
    await runMigrations();
    await clearTables();
    await importWords();
    await importProverbs();
    await runSeedSQL();
    await analyzeDatabase();
    
    console.log('🎉 Data import completed successfully!');
    
  } catch (error) {
    console.error('💥 Import failed:', error);
    process.exit(1);
  } finally {
    await postgresConnection.disconnect();
    console.log('👋 Disconnected from PostgreSQL');
  }
}

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Import interrupted by user');
  await postgresConnection.disconnect();
  process.exit(0);
});

// Run the import
main();
