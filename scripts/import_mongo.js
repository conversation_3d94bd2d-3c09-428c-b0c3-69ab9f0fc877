#!/usr/bin/env node

/**
 * MongoDB Data Import Script
 * 
 * This script imports word and proverb data into MongoDB
 * Usage: node scripts/import_mongo.js [--words] [--proverbs] [--clear]
 */

const mongoose = require('mongoose');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

// Import models
const Word = require('../src/db/mongo/models/Word');
const Proverb = require('../src/db/mongo/models/Proverb');

// Command line arguments
const args = process.argv.slice(2);
const shouldImportWords = args.includes('--words') || args.length === 0;
const shouldImportProverbs = args.includes('--proverbs') || args.length === 0;
const shouldClear = args.includes('--clear');

async function connectToDatabase() {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/shona_dictionary';
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function clearCollections() {
  if (!shouldClear) return;
  
  try {
    if (shouldImportWords) {
      await Word.deleteMany({});
      console.log('🗑️  Cleared words collection');
    }
    
    if (shouldImportProverbs) {
      await Proverb.deleteMany({});
      console.log('🗑️  Cleared proverbs collection');
    }
  } catch (error) {
    console.error('❌ Error clearing collections:', error);
    throw error;
  }
}

async function importWords() {
  if (!shouldImportWords) return;
  
  try {
    const wordsPath = path.join(__dirname, '../src/db/mongo/seed/words.json');
    const wordsData = await fs.readFile(wordsPath, 'utf8');
    const words = JSON.parse(wordsData);
    
    console.log(`📚 Importing ${words.length} words...`);
    
    for (const wordData of words) {
      try {
        const word = new Word(wordData);
        await word.save();
        console.log(`  ✅ Imported: ${wordData.shona} -> ${wordData.english}`);
      } catch (error) {
        console.error(`  ❌ Failed to import word: ${wordData.shona}`, error.message);
      }
    }
    
    const totalWords = await Word.countDocuments();
    console.log(`📊 Total words in database: ${totalWords}`);
    
  } catch (error) {
    console.error('❌ Error importing words:', error);
    throw error;
  }
}

async function importProverbs() {
  if (!shouldImportProverbs) return;
  
  try {
    // Check if proverbs.json exists, if not create sample data
    const proverbsPath = path.join(__dirname, '../src/db/mongo/seed/proverbs.json');
    let proverbs;
    
    try {
      const proverbsData = await fs.readFile(proverbsPath, 'utf8');
      proverbs = JSON.parse(proverbsData);
    } catch (fileError) {
      // Create sample proverbs if file doesn't exist
      proverbs = [
        {
          shona: "Chakafukidza dzimba matenga",
          english: "What covers houses is the roof",
          meaning: "Every problem has a solution, just as every house needs a roof for protection",
          context: "Used when encouraging someone facing difficulties",
          category: "wisdom",
          difficulty: "intermediate",
          tags: ["wisdom", "solutions", "problems"]
        },
        {
          shona: "Chara chimwe hachitswanyi inda",
          english: "One finger cannot crush a louse",
          meaning: "Unity is strength; working together achieves more than working alone",
          context: "Used to emphasize the importance of cooperation",
          category: "wisdom",
          difficulty: "intermediate",
          tags: ["unity", "cooperation", "teamwork"]
        }
      ];
      
      // Save sample data to file
      await fs.writeFile(proverbsPath, JSON.stringify(proverbs, null, 2));
      console.log('📝 Created sample proverbs.json file');
    }
    
    console.log(`🎭 Importing ${proverbs.length} proverbs...`);
    
    for (const proverbData of proverbs) {
      try {
        const proverb = new Proverb(proverbData);
        await proverb.save();
        console.log(`  ✅ Imported: ${proverbData.shona}`);
      } catch (error) {
        console.error(`  ❌ Failed to import proverb: ${proverbData.shona}`, error.message);
      }
    }
    
    const totalProverbs = await Proverb.countDocuments();
    console.log(`📊 Total proverbs in database: ${totalProverbs}`);
    
  } catch (error) {
    console.error('❌ Error importing proverbs:', error);
    throw error;
  }
}

async function createIndexes() {
  try {
    console.log('🔍 Creating database indexes...');
    
    if (shouldImportWords) {
      await Word.createIndexes();
      console.log('  ✅ Word indexes created');
    }
    
    if (shouldImportProverbs) {
      await Proverb.createIndexes();
      console.log('  ✅ Proverb indexes created');
    }
    
  } catch (error) {
    console.error('❌ Error creating indexes:', error);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting MongoDB data import...');
    console.log(`📋 Import options: words=${shouldImportWords}, proverbs=${shouldImportProverbs}, clear=${shouldClear}`);
    
    await connectToDatabase();
    await clearCollections();
    await importWords();
    await importProverbs();
    await createIndexes();
    
    console.log('🎉 Data import completed successfully!');
    
  } catch (error) {
    console.error('💥 Import failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from MongoDB');
  }
}

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Import interrupted by user');
  await mongoose.disconnect();
  process.exit(0);
});

// Run the import
main();
