const request = require('supertest');
const app = require('../../src/app');

describe('Word API Endpoints', () => {
  describe('GET /api/words', () => {
    it('should return all words with pagination', async () => {
      const response = await request(app)
        .get('/api/words')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('words');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.words)).toBe(true);
    });

    it('should filter words by difficulty', async () => {
      const response = await request(app)
        .get('/api/words?difficulty=beginner')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.words.every(word => 
        word.difficulty === 'beginner'
      )).toBe(true);
    });

    it('should filter words by part of speech', async () => {
      const response = await request(app)
        .get('/api/words?partOfSpeech=noun')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.words.every(word => 
        word.partOfSpeech === 'noun' || word.part_of_speech === 'noun'
      )).toBe(true);
    });
  });

  describe('GET /api/words/search', () => {
    it('should search words successfully', async () => {
      const response = await request(app)
        .get('/api/words/search?q=mhuri')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('words');
      expect(response.body.data).toHaveProperty('pagination');
    });

    it('should return error for empty search query', async () => {
      const response = await request(app)
        .get('/api/words/search')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Search query is required');
    });

    it('should search in specific language', async () => {
      const response = await request(app)
        .get('/api/words/search?q=family&lang=english')
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('POST /api/words', () => {
    const validWord = {
      shona: 'test',
      english: 'test',
      partOfSpeech: 'noun',
      definition: 'A test word',
      difficulty: 'beginner'
    };

    it('should create a new word successfully', async () => {
      const response = await request(app)
        .post('/api/words')
        .send(validWord)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('shona', 'test');
    });

    it('should return validation error for missing required fields', async () => {
      const invalidWord = {
        shona: 'test'
        // missing required fields
      };

      const response = await request(app)
        .post('/api/words')
        .send(invalidWord)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    it('should return validation error for invalid part of speech', async () => {
      const invalidWord = {
        ...validWord,
        partOfSpeech: 'invalid'
      };

      const response = await request(app)
        .post('/api/words')
        .send(invalidWord)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('GET /api/words/:id', () => {
    it('should return 404 for non-existent word', async () => {
      const response = await request(app)
        .get('/api/words/999999')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('not found');
    });
  });
});

describe('API Health Check', () => {
  it('should return health status', async () => {
    const response = await request(app)
      .get('/api/health')
      .expect(200);

    expect(response.body.status).toBe('OK');
    expect(response.body.message).toContain('Shona Dictionary API is running');
  });
});

describe('API Documentation', () => {
  it('should return API documentation', async () => {
    const response = await request(app)
      .get('/api')
      .expect(200);

    expect(response.body.message).toContain('Welcome to Shona Dictionary API');
    expect(response.body.endpoints).toBeDefined();
  });
});
