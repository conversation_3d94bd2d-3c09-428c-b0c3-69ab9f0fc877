const request = require('supertest');
const app = require('../../src/app');

// Integration tests for the complete API workflow
describe('API Integration Tests', () => {
  let createdWordId;
  let createdProverbId;

  describe('Complete Word Workflow', () => {
    const testWord = {
      shona: 'integration-test',
      english: 'integration test',
      partOfSpeech: 'noun',
      definition: 'A word created during integration testing',
      examples: [
        {
          shona: 'Iri integration-test',
          english: 'This is an integration test'
        }
      ],
      pronunciation: 'in-te-gra-tion-test',
      difficulty: 'advanced',
      tags: ['testing', 'integration']
    };

    it('should create a new word', async () => {
      const response = await request(app)
        .post('/api/words')
        .send(testWord)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.shona).toBe(testWord.shona);
      
      // Store the ID for subsequent tests
      createdWordId = response.body.data.id || response.body.data._id;
      expect(createdWordId).toBeDefined();
    });

    it('should retrieve the created word by ID', async () => {
      const response = await request(app)
        .get(`/api/words/${createdWordId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.shona).toBe(testWord.shona);
      expect(response.body.data.english).toBe(testWord.english);
    });

    it('should find the word in search results', async () => {
      const response = await request(app)
        .get('/api/words/search?q=integration-test')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.words.length).toBeGreaterThan(0);
      
      const foundWord = response.body.data.words.find(word => 
        word.shona === testWord.shona
      );
      expect(foundWord).toBeDefined();
    });

    it('should update the created word', async () => {
      const updateData = {
        definition: 'An updated definition for integration testing',
        difficulty: 'intermediate'
      };

      const response = await request(app)
        .put(`/api/words/${createdWordId}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.definition).toBe(updateData.definition);
      expect(response.body.data.difficulty).toBe(updateData.difficulty);
    });

    it('should include the word in filtered results', async () => {
      const response = await request(app)
        .get('/api/words?difficulty=intermediate&partOfSpeech=noun')
        .expect(200);

      expect(response.body.success).toBe(true);
      
      const foundWord = response.body.data.words.find(word => 
        (word.shona || word.shona) === testWord.shona
      );
      expect(foundWord).toBeDefined();
    });

    it('should delete the created word', async () => {
      const response = await request(app)
        .delete(`/api/words/${createdWordId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should return 404 when trying to access deleted word', async () => {
      const response = await request(app)
        .get(`/api/words/${createdWordId}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('Pagination and Filtering', () => {
    it('should handle pagination correctly', async () => {
      const page1Response = await request(app)
        .get('/api/words?page=1&limit=2')
        .expect(200);

      expect(page1Response.body.data.pagination.page).toBe(1);
      expect(page1Response.body.data.pagination.limit).toBe(2);
      expect(page1Response.body.data.words.length).toBeLessThanOrEqual(2);

      if (page1Response.body.data.pagination.pages > 1) {
        const page2Response = await request(app)
          .get('/api/words?page=2&limit=2')
          .expect(200);

        expect(page2Response.body.data.pagination.page).toBe(2);
        
        // Ensure different results on different pages
        const page1Ids = page1Response.body.data.words.map(w => w.id || w._id);
        const page2Ids = page2Response.body.data.words.map(w => w.id || w._id);
        
        const intersection = page1Ids.filter(id => page2Ids.includes(id));
        expect(intersection.length).toBe(0);
      }
    });

    it('should handle invalid pagination parameters', async () => {
      const response = await request(app)
        .get('/api/words?page=0&limit=101')
        .expect(200); // Should still work with defaults

      expect(response.body.success).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/words')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should handle very long search queries', async () => {
      const longQuery = 'a'.repeat(200);
      const response = await request(app)
        .get(`/api/words/search?q=${longQuery}`)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should return 404 for non-existent endpoints', async () => {
      const response = await request(app)
        .get('/api/nonexistent')
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    it('should handle multiple requests within rate limit', async () => {
      const requests = Array(5).fill().map(() => 
        request(app).get('/api/health')
      );

      const responses = await Promise.all(requests);
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });
  });
});
